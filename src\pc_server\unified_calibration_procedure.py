"""
Unified calibration procedure using pygame GUI.
Shows each calibration point once and performs validation on the same data.
Stores averaged eye_center and glints in addition to gaze_origin and gaze_vec.
"""

import time
import logging
import numpy as np
import json
import datetime
from queue import Queue, Empty as QueueEmpty
from threading import Thread
from typing import List, Optional, Dict, Any

from src.common.eye_data import FrameData, CalibrPoint, EyeData
from src.common.config import Conf
from screeninfo import Monitor
from src.common.json_encoder import J<PERSON>NEncoder
from src.pc_server.calibration_models import CalibrationModel
from src.pc_server.pygame_calibration_gui import PygameCalibrationGUI


class UnifiedCalibrationProcedure:
    """
    Unified calibration procedure that combines calibration and validation.
    Uses pygame for GUI and stores comprehensive eye tracking data.
    """
    
    FRAME_TIMEOUT = 0.2
    
    def __init__(self, 
                 calibration_points: List[CalibrPoint],
                 q_cal_frame_data: Queue,
                 q_cal_commands: Queue,
                 q_cal_data: Queue,
                 conf: Conf,
                 monitor: Monitor):
        """
        Initialize the unified calibration procedure.
        
        Args:
            calibration_points: List of calibration points
            q_cal_frame_data: Queue to receive frame data
            q_cal_commands: Queue to receive commands
            q_cal_data: Queue to send calibration results
            conf: Configuration object
            monitor: Monitor for display
        """
        self.conf = conf
        self.monitor = monitor
        self.calibration_points = calibration_points
        self.n_points = len(calibration_points)
        
        # Queues
        self.q_cal_frame_data = q_cal_frame_data
        self.q_cal_commands = q_cal_commands
        self.q_cal_data = q_cal_data
        
        # GUI
        self.gui = PygameCalibrationGUI(monitor)
        
        # Calibration model
        self.calibration_model = CalibrationModel(monitor, conf)
        
        # Data collection
        self.current_point_data = []
        self.all_frame_data: List[List[FrameData]] = []
        self.recorded_data = []
        
        # Timing and control
        self.start_ticks = 0
        self.start_point_time = 0
        self.skip_cnt = 0
        self.skip = 3 if self.conf['fps'] > 250 else 0
        self.num_points_avg = int(self.conf['fps'] * self.conf['calibr_duration'])
        
        # Results
        self.calibr_json = {
            'aborted': False, 
            'summary': [{}] * self.n_points, 
            'corners': [{}] * self.n_points
        }
        
    def check_gaze_ok(self, data: FrameData) -> bool:
        """Check if gaze data quality is acceptable."""
        for eye_data in data.eye_data:
            if (np.isnan(eye_data.gaze_origin).any() or 
                np.isnan(eye_data.gaze_vec).any()):
                return False
        return True
        
    def current_point_calibrated(self) -> bool:
        """Check if current point has enough data for calibration."""
        return len(self.current_point_data) >= self.num_points_avg
        
    def numpy_gaze(self, data: FrameData) -> np.ndarray:
        """Convert FrameData to numpy array format for processing."""
        gaze = np.full((2, 2, 3), np.nan)
        for i, eye_data in enumerate(data.eye_data):
            if not np.isnan(eye_data.gaze_origin).any():
                gaze[i, 0, :] = eye_data.gaze_origin
                gaze[i, 1, :] = eye_data.gaze_vec
        return gaze
        
    def get_frame_data_with_timeout(self) -> Optional[FrameData]:
        """Get frame data from queue with timeout."""
        try:
            return self.q_cal_frame_data.get(timeout=self.FRAME_TIMEOUT)
        except QueueEmpty:
            time.sleep(self.FRAME_TIMEOUT)
            return None
            
    def process_frame_data(self, data: FrameData, waiting: bool) -> bool:
        """
        Process incoming frame data and update GUI.
        
        Args:
            data: Frame data to process
            waiting: Whether we're waiting for user to start point
            
        Returns:
            True if data was collected, False otherwise
        """
        # Skip frames if needed for performance
        self.skip_cnt += 1
        if self.skip_cnt <= self.skip:
            return False
        self.skip_cnt = 0
        
        # Determine frame color based on gaze quality
        if self.check_gaze_ok(data):
            frame_color = self.gui.FRAME_OK_COLOR
            data_collected = False
            
            # Collect data if not waiting and enough time has passed
            if (not waiting and 
                self.start_ticks != 0 and 
                data.ticks >= self.start_ticks and
                time.time() - self.start_point_time > 0.5):
                
                self.current_point_data.append(self.numpy_gaze(data))
                self.all_frame_data[-1].append(data)
                data_collected = True
                
                # Limit data collection size
                if len(self.current_point_data) > self.conf['calibr_duration_ratio'] * self.num_points_avg:
                    self.current_point_data.pop(0)
                    self.all_frame_data[-1].pop(0)
                    
        else:
            frame_color = self.gui.FRAME_BAD_COLOR
            data_collected = False
            
        # Update GUI with frame border
        self.gui.draw_frame_border(frame_color)
        
        return data_collected
        
    def calibrate_single_point(self, point_index: int, point: CalibrPoint) -> Optional[str]:
        """
        Calibrate a single point.
        
        Args:
            point_index: Index of the point being calibrated
            point: Calibration point object
            
        Returns:
            'abort' if aborted, None if successful
        """
        logging.info(f'Starting calibration point {point_index + 1}/{self.n_points}')
        
        # Initialize data collection for this point
        self.all_frame_data.append([])
        self.current_point_data = []
        
        # Clear screen and show waiting message
        self.gui.clear_screen()
        self.gui.draw_calibration_point(point.pog[0], point.pog[1])
        self.gui.draw_text(f"Point {point_index + 1}/{self.n_points} - Press SPACE to start", 
                          0.5, 0.1)
        self.gui.update_display()
        
        # Wait for user to start calibration
        while True:
            command = self.gui.handle_events()
            if command == 'abort':
                self.calibr_json['aborted'] = True
                return 'abort'
            elif command == 'start':
                self.start_ticks = time.time() * 1000  # Convert to milliseconds
                self.start_point_time = time.time()
                break
                
            # Process frame data while waiting
            data = self.get_frame_data_with_timeout()
            if data:
                self.process_frame_data(data, waiting=True)
                self.gui.update_display()
                
            self.gui.tick()
            
        logging.info(f'Collecting data for point {point_index + 1}')
        
        # Clear screen and show collection message
        self.gui.clear_screen()
        self.gui.draw_calibration_point(point.pog[0], point.pog[1])
        self.gui.draw_text(f"Look at the point - {point_index + 1}/{self.n_points}", 
                          0.5, 0.1)
        self.gui.update_display()
        
        # Collect calibration data
        while not self.current_point_calibrated():
            command = self.gui.handle_events()
            if command == 'abort':
                self.calibr_json['aborted'] = True
                return 'abort'
                
            data = self.get_frame_data_with_timeout()
            if data:
                self.process_frame_data(data, waiting=False)
                self.gui.update_display()
                
            self.gui.tick()
            
        # Brief pause after collection
        time.sleep(0.5)
        
        logging.info(f'Point {point_index + 1} calibrated successfully')
        return None

    def group_consistent_data(self, data_points: List[np.ndarray]) -> List[List[np.ndarray]]:
        """
        Group data points by consistency using distance threshold.

        Args:
            data_points: List of gaze data arrays

        Returns:
            List of groups, each containing consistent data points
        """
        def get_distance(p1: np.ndarray, p2: np.ndarray) -> float:
            """Calculate distance between two gaze data points."""
            # Compare gaze vectors (direction)
            vec1 = p1[:, 1, :]  # gaze vectors for both eyes
            vec2 = p2[:, 1, :]

            # Calculate angular distance
            valid_mask = ~(np.isnan(vec1).any(axis=1) | np.isnan(vec2).any(axis=1))
            if not valid_mask.any():
                return float('inf')

            # Use dot product to calculate angular distance
            dots = np.sum(vec1[valid_mask] * vec2[valid_mask], axis=1)
            dots = np.clip(dots, -1, 1)  # Ensure valid range for arccos
            angles = np.arccos(dots)
            return np.mean(angles)

        points = data_points.copy()
        groups = []

        while points:
            # Start new group with first remaining point
            ref_point = points.pop(0)
            current_group = [ref_point]
            remaining_points = []

            # Find all points consistent with reference
            for point in points:
                distance = get_distance(ref_point, point)
                if distance < self.conf['calibr_sin_thr']:
                    current_group.append(point)
                else:
                    remaining_points.append(point)

            groups.append(current_group)
            points = remaining_points

        return groups

    def average_eye_data(self, frame_data_list: List[FrameData]) -> Dict[str, EyeData]:
        """
        Calculate averaged eye data from frame data list.

        Args:
            frame_data_list: List of FrameData objects

        Returns:
            Dictionary with 'left' and 'right' averaged EyeData
        """
        averaged_data = {'left': EyeData(), 'right': EyeData()}

        for eye_idx, eye_name in enumerate(['left', 'right']):
            # Collect all valid data for this eye
            eye_centers = []
            glints_list = []
            gaze_origins = []
            gaze_vecs = []

            for frame_data in frame_data_list:
                eye_data = frame_data.eye_data[eye_idx]

                # Collect eye center if valid
                if not np.isnan(eye_data.eye_center).any():
                    eye_centers.append(eye_data.eye_center)

                # Collect glints if valid
                if not np.isnan(eye_data.glints).any():
                    glints_list.append(eye_data.glints)

                # Collect gaze data if valid
                if not np.isnan(eye_data.gaze_origin).any():
                    gaze_origins.append(eye_data.gaze_origin)

                if not np.isnan(eye_data.gaze_vec).any():
                    gaze_vecs.append(eye_data.gaze_vec)

            # Calculate averages
            if eye_centers:
                averaged_data[eye_name].eye_center = np.mean(eye_centers, axis=0)

            if glints_list:
                averaged_data[eye_name].glints = np.mean(glints_list, axis=0)

            if gaze_origins:
                averaged_data[eye_name].gaze_origin = np.mean(gaze_origins, axis=0)

            if gaze_vecs:
                averaged_data[eye_name].gaze_vec = np.mean(gaze_vecs, axis=0)
                # Normalize the averaged gaze vector
                norm = np.linalg.norm(averaged_data[eye_name].gaze_vec)
                if norm > 0:
                    averaged_data[eye_name].gaze_vec /= norm

        return averaged_data

    def get_averaged_calibration_point(self, point: CalibrPoint, point_index: int) -> CalibrPoint:
        """
        Calculate averaged calibration point from collected data.

        Args:
            point: Original calibration point
            point_index: Index of the point

        Returns:
            Updated calibration point with averaged data
        """
        # Group data by consistency
        groups = self.group_consistent_data(self.current_point_data)

        # Use the largest group for averaging
        if not groups:
            logging.warning(f"No valid data groups for point {point_index}")
            return point

        group_sizes = [len(group) for group in groups]
        largest_group_idx = np.argmax(group_sizes)
        largest_group = groups[largest_group_idx]

        logging.info(f"Point {point_index}: Using group of {len(largest_group)} samples "
                    f"out of {len(self.current_point_data)} total")

        # Get corresponding frame data for the largest group
        # This is a simplified approach - in practice, you'd need to track
        # which frame data corresponds to which numpy gaze data
        frame_data_subset = self.all_frame_data[point_index][-len(largest_group):]

        # Calculate averaged eye data
        averaged_eye_data = self.average_eye_data(frame_data_subset)

        # Update calibration point
        updated_point = CalibrPoint()
        updated_point.pog = point.pog.copy()
        updated_point.eye_data[0] = averaged_eye_data['left']
        updated_point.eye_data[1] = averaged_eye_data['right']

        return updated_point

    def validate_calibration(self) -> str:
        """
        Validate calibration using the collected data.

        Returns:
            'ok' if validation passes, 'repeat' if it fails, 'abort' if aborted
        """
        logging.info("Starting validation on collected calibration data")

        bad_points = 0
        validation_errors = []

        # Show validation screen
        self.gui.clear_screen()
        self.gui.draw_text("Validating calibration...", 0.5, 0.5)
        self.gui.update_display()

        # Validate each calibration point using its collected data
        for i, point in enumerate(self.calibration_points):
            if i >= len(self.all_frame_data):
                continue

            frame_data_list = self.all_frame_data[i]
            if not frame_data_list:
                bad_points += 1
                continue

            # Calculate POGs for this point's data using calibration model
            point_pogs = []
            target_pog = np.array(point.pog)

            for frame_data in frame_data_list:
                # Use calibration model to calculate POG
                try:
                    pog = self.calibration_model.calc_pog(frame_data)
                    if pog is not None and not np.isnan(pog).any():
                        point_pogs.append(pog)
                except Exception as e:
                    logging.warning(f"Error calculating POG for validation: {e}")
                    continue

            if not point_pogs:
                bad_points += 1
                continue

            # Calculate validation metrics
            point_pogs = np.array(point_pogs)
            mean_pog = np.mean(point_pogs, axis=0)
            error_vector = target_pog - mean_pog
            error_magnitude = np.linalg.norm(error_vector)

            validation_errors.append(error_magnitude)

            # Check if error exceeds threshold
            if error_magnitude > self.conf['calibr_validation_threshold']:
                bad_points += 1

            logging.info(f"Point {i+1} validation error: {error_magnitude:.3f}")

        # Show validation results
        self.gui.clear_screen()

        if bad_points == 0:
            self.gui.draw_text("Calibration Successful!", 0.5, 0.4, color=self.gui.GREEN)
            self.gui.draw_text("Press SPACE to continue or ESC to abort", 0.5, 0.6)
            result = 'ok'
        else:
            self.gui.draw_text(f"Calibration Failed: {bad_points}/{self.n_points} points",
                              0.5, 0.4, color=self.gui.RED)
            self.gui.draw_text("Press SPACE to retry or ESC to abort", 0.5, 0.6)
            result = 'repeat'

        self.gui.update_display()

        # Wait for user response
        user_response = self.gui.wait_for_space_or_escape()
        if user_response == 'abort':
            return 'abort'

        return result

    def run(self) -> Optional[List[CalibrPoint]]:
        """
        Run the complete calibration procedure.

        Returns:
            List of calibrated points if successful, None if aborted
        """
        logging.info("Starting unified calibration procedure")

        try:
            self.gui.start()

            while True:
                # Reset data for new calibration attempt
                self.all_frame_data = []
                self.recorded_data = []

                # Calibrate each point
                for i, point in enumerate(self.calibration_points):
                    result = self.calibrate_single_point(i, point)
                    if result == 'abort':
                        self.record_calibration_json()
                        return None

                    # Process and store the calibrated point
                    if self.conf['send_deep_diagnostics'] != 'never':
                        self.recorded_data.append({
                            'point': point,
                            'data': self.current_point_data.copy()
                        })

                    # Update calibration point with averaged data
                    self.calibration_points[i] = self.get_averaged_calibration_point(point, i)

                    # Update summary for JSON output
                    cp = self.calibration_points[i].eye_data
                    self.calibr_json['summary'][i] = {
                        'left': f'{cp[0].gaze_vec[0]:.3f}, {cp[0].gaze_vec[1]:.3f}',
                        'right': f'{cp[1].gaze_vec[0]:.3f}, {cp[1].gaze_vec[1]:.3f}'
                    }

                # Set calibration data in model
                self.calibration_model.calibr_data = self.calibration_points

                # Validate calibration
                validation_result = self.validate_calibration()

                if validation_result == 'abort':
                    self.record_calibration_json()
                    return None
                elif validation_result == 'ok':
                    # Calibration successful
                    self.record_calibration_json()
                    self.record_deep_diagnostics()
                    logging.info("Calibration completed successfully")
                    return self.calibration_points
                elif validation_result == 'repeat':
                    # Retry calibration
                    logging.info("Retrying calibration")
                    continue

        except Exception as e:
            logging.error(f"Error during calibration: {e}")
            self.calibr_json['aborted'] = True
            self.record_calibration_json()
            return None
        finally:
            self.gui.stop()

    def record_calibration_json(self):
        """Record calibration results to JSON file."""
        try:
            timestamp = datetime.datetime.now().strftime('%Y_%m_%d__%H_%M_%S')
            filename = f'{self.conf["session_data_dir"]}calibr_result_{timestamp}.json'
            with open(filename, 'w') as fp:
                json.dump(self.calibr_json, fp, indent=2)
            logging.info(f"Calibration results saved to {filename}")
        except Exception as e:
            logging.error(f"Error saving calibration JSON: {e}")

    def record_deep_diagnostics(self):
        """Record detailed diagnostic data if enabled."""
        if (self.conf['send_deep_diagnostics'] != 'never' and
            len(self.recorded_data) > 0):
            try:
                timestamp = datetime.datetime.now().strftime('%Y_%m_%d__%H_%M_%S')
                filename = f'{self.conf["session_data_dir"]}deep_diag/calibr_points_{timestamp}.json'
                with open(filename, 'w') as fp:
                    json.dump(self.recorded_data, fp, indent=2, cls=JSONEncoder)
                logging.info(f"Deep diagnostics saved to {filename}")
            except Exception as e:
                logging.error(f"Error saving deep diagnostics: {e}")
