import logging
import shutil
from threading import Thread
from queue import Queue
import time
import multiprocessing as mp
import platform
import psutil
import screeninfo
import json
import base64
import os
import sys

from . import nvidia_smi
from src.common.chrono import Chrono
from .open_gaze_api import OpenGazeAPI, SocketProcess
from src.common.eye_data import *
from src.common.config import *
from .calibration_models import CalibrationModel
from .calibration_procedure import CalibrationProcedure
from .unified_calibration_procedure import UnifiedCalibrationProcedure
from .fastapi_proc import FastapiProc
from src.common.json_encoder import JSONEncoder
    
    
class PCServer:
    CAL_POINTS = [[0.1, 0.1], [0.9, 0.1], [0.1, 0.9], [0.9, 0.9]]
    # VAL_POINTS = [[0.5, 0.1], [0.9, 0.5], [0.5, 0.9], [0.1, 0.5]]
    VAL_POINTS = [[0.1, 0.1], [0.5, 0.1], [0.9, 0.1],
                  [0.1, 0.5], [0.5, 0.5], [0.9, 0.5],
                  [0.1, 0.9], [0.5, 0.9], [0.9, 0.9],]
    # MAX_BAD_POINTS = 2

    def __init__(self, 
                 q_frame_data: mp.Queue, 
                 q_diag_data:mp.Queue, 
                 q_frames: mp.Queue, 
                 conf: Conf, 
                 camera_data: dict,
                 monitor: screeninfo.Monitor|None = None,
                 emulation=False):
        self.q_frame_data = q_frame_data
        self.q_diag_data = q_diag_data
        self.q_frames = q_frames
        if monitor is None:
            screens = screeninfo.get_monitors()
            for s in screens:
                if s.is_primary:
                    monitor = s
                    break
        assert monitor is not None
        self.monitor = monitor
        self.conf = conf
        self.calibration_model = CalibrationModel(self.monitor, self.conf)
        self.q_cal_frame_data = Queue()
        self.q_cal_commands = Queue()
        self.q_cal_data = Queue()
        self.calibration_procedure = None
        self._calibr_start = False
        self._calibr_show = False
        self._last_calibr_ok = False
        self.emulation = emulation

        self.api_send = Queue()
        self.api_recv = Queue()
        self.q_records = Queue()
        self.og_api = OpenGazeAPI(self.api_send, self.api_recv, self.q_records, self, self.conf['use_socket'])
        self.api_proc = None
        
        #chronos = ['get', 'detection', 'iris_seg', 'pupil_seg', 'frame', 'frame_total']
        self.chronos_perf:dict[str, Chrono] = {}
        self.chronos_lat:dict[str, Chrono] = {}


        self.loop_time = None
        self.frame_counter = 0
        self.diag_dir = ''
        self.diag_rate = 0
        self.diag_counter = 0
        if self.conf['send_diagnostics_fpm']:
            self.diag_dir = self.conf['session_data_dir'] + 'diag/'
            os.makedirs(self.diag_dir, exist_ok=True)
            self.diag_rate = int(np.ceil(np.abs(60*self.conf['stream_fps']/self.conf['send_diagnostics_fpm'])))
        if self.conf['use_socket']:
            self.transport_proc = Thread(target=self.init_socket_api_proc, name='og_api_socket',
                                args=(self, self.api_send, self.api_recv))
        else:
            self.transport_proc = Thread(target=self.init_fastapi_proc, name='fastapi_proc',
                                        args=(self.q_frames, self.api_send, self.api_recv, self.q_records))
        self.transport_proc.daemon = True
        self.transport_proc.start()

        self.enable_writing_diagnostics = True
        self.calc_disk_space()

        self.start_time = time.perf_counter()
        self.last_timestamp = self.start_time
        self.systeminfo = {}
        self.camera_info = {}
        if os.path.isfile('camera_info.json'):
            with open('camera_info.json') as f:
                self.camera_info = json.load(f)
        self.get_platform_info()

        Thread(target = self.calc_disk_space_thread, name='calc_disk_space_thread', daemon=True).start()

    def get_machine_info(self):
        disks = [d._asdict() for d in psutil.disk_partitions()]
        gpus = []
        try:
            gpus = nvidia_smi.get_gpus()
        except:
            pass
        for d in disks:
            try:
                d["usage"] = psutil.disk_usage(d["mountpoint"])._asdict()
            except:
                pass
        machine = {
                "RAM": psutil.virtual_memory()._asdict(), 
                "swap": psutil.swap_memory()._asdict(),
                "cpu": {
                    "count": psutil.cpu_count(logical=False),
                    "count_logical": psutil.cpu_count(logical=True),
                    "freq": psutil.cpu_freq()._asdict(),
                    "percent": psutil.cpu_percent(percpu = True)
                },
                "disks": disks,
                "disk_io": {k: v._asdict() for k, v in psutil.disk_io_counters(perdisk=True).items()},
                "gpus": gpus
            },
        return machine
        
    #TODO: platform_utils to separate file
    def get_platform_info(self):
        self.systeminfo = {
            "meta": {
                "current_time": datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f'),
                "platform": {
                    "platform": platform.platform(),
                    "architecture": ", ".join(platform.architecture()),
                    "machine": platform.machine(),
                    "node": platform.node(),
                    "processor": platform.processor(),
                    "release": platform.release(),
                    "system": platform.system(),
                    "version": platform.version(),
                    "machine_info": self.get_machine_info(),
                    "python": {
                        "build": ", ".join(platform.python_build()),
                        "compiler": platform.python_compiler(),
                        "branch": platform.python_branch(),
                        "implementation": platform.python_implementation(),
                        "revision": platform.python_revision(),
                        "version": platform.python_version()
                    },
                    "win32": {},
                    "unix": {},
                    "linux": {},
                    "displays": [x.__dict__ for x in screeninfo.get_monitors()],
                    "camera": self.camera_info,
                    "pathfinder_version": ""
                },
                "settings": {
                    "TYPE": "TCP" if ['use_socket'] else "HTTP",
                    "VIDEO_TYPE": "DEMO" if self.conf['video_file'] else "CAMERA",
                    "PROCESSOR": "NVIDIA" if self.conf['use_gpu'] else "INTEL",
                    "FREQ": int(self.conf['fps'])
                }
            }
        }
        try:
            self.systeminfo["meta"]["platform"]["win32"] = {
                "ver": ", ".join(platform.win32_ver()),
                "edition": platform.win32_edition(),
                "is_iot": platform.win32_is_iot(),
            }
        except:
            pass
        try:
            self.systeminfo["meta"]["platform"]["unix"] = {
                "libc_ver": " ".join(platform.libc_ver()), 
            }
        except:
            pass
        try:
            self.systeminfo["meta"]["platform"]["linux"] = platform.freedesktop_os_release()
        except:
            pass
        try:
            try:
                base_path = sys._MEIPASS
            except:
                base_path = ''
            with open(os.path.join(base_path, "version.txt")) as fp:
                self.systeminfo["meta"]["platform"]["pathfinder_version"] = fp.read()
        except:
            pass

    # @staticmethod
    def init_fastapi_proc(self, q_frames: Queue, q_send: Queue, q_recv: Queue, q_records: Queue):
        self.api_proc = FastapiProc(q_frames, q_send, q_recv, q_records)
        self.api_proc.run()

    @staticmethod
    def init_socket_api_proc(q_send: Queue, q_recv: Queue):
        api_proc = SocketProcess(q_send, q_recv)
        api_proc.run()

    @staticmethod
    def init_calibr_proc(cal_points,
                         val_points,
                         q_cal_frame_data: Queue,
                         q_cal_commands: Queue,
                         q_cal_data: Queue,
                         conf: Conf,
                         monitor: screeninfo.Monitor
                         ):
        cal_proc = CalibrationProcedure(cal_points, val_points,
                                        q_cal_frame_data, q_cal_commands,
                                        q_cal_data, conf, monitor)
        cal_proc.run()

    @staticmethod
    def init_unified_calibr_proc(cal_points,
                                q_cal_frame_data: Queue,
                                q_cal_commands: Queue,
                                q_cal_data: Queue,
                                conf: Conf,
                                monitor: screeninfo.Monitor
                                ):
        """Initialize unified calibration procedure with pygame GUI."""
        cal_proc = UnifiedCalibrationProcedure(cal_points,
                                              q_cal_frame_data, q_cal_commands,
                                              q_cal_data, conf, monitor)
        result = cal_proc.run()
        if result is not None:
            q_cal_data.put(result)

    @property
    def calibr_start(self):
        return self._calibr_start

    @calibr_start.setter
    def calibr_start(self, val: bool):
        if self._calibr_start != val:
            self._calibr_start = val
            if val:
                if self.api_proc is not None:
                    self.api_proc.clear_queues()

                cal_points = []
                for p in self.CAL_POINTS:
                    cp = CalibrPoint()
                    cp.pog = p
                    cal_points.append(cp)
                val_points = []
                for p in self.VAL_POINTS:
                    cp = CalibrPoint()
                    cp.pog = p
                    val_points.append(cp)
                # Choose calibration procedure based on configuration
                use_unified_calibration = self.conf.get('use_unified_calibration', True)

                if use_unified_calibration:
                    # Use new unified calibration procedure with pygame
                    Thread(target=self.init_unified_calibr_proc,
                           args=(cal_points,
                                 self.q_cal_frame_data, self.q_cal_commands,
                                 self.q_cal_data, self.conf, self.monitor),
                           name='unified_calibration',
                           daemon=True).start()
                else:
                    # Use original calibration procedure with OpenCV
                    Thread(target=self.init_calibr_proc,
                           args=(cal_points, val_points,
                                 self.q_cal_frame_data, self.q_cal_commands,
                                 self.q_cal_data, self.conf, self.monitor),
                           name='calibration',
                           daemon=True).start()
            else:
                self.start_time = self.last_timestamp
                self.og_api.settings['CALIBRATE_START'].value = False
                # with self.q_records.mutex:
                #     self.q_records.queue.clear()

    @property
    def calibr_show(self):
        return self._calibr_show

    @calibr_show.setter
    def calibr_show(self, val: bool):
        self._calibr_show = val
        # TODO

    @property
    def last_calibr_ok(self):
        return self._last_calibr_ok
        
    @last_calibr_ok.setter
    def last_calibr_ok(self, val:bool):
        if val != self._last_calibr_ok:
            self.og_api.settings['LAST_CALIBR_RESULT'].value = val
            self._last_calibr_ok = val
    
    def send_diag_data(self, frame, record_data):
        if self.camera_info == {} and os.path.isfile('camera_info.json'):
            with open('camera_info.json') as f:
                self.camera_info = json.load(f)
                self.systeminfo["meta"]["platform"]["camera"] = self.camera_info
        frame_64 = base64.b64encode(frame).decode('utf-8')
        self.systeminfo["meta"]["current_time"] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f')
        self.systeminfo["meta"]["settings"] |= self.og_api.get_settings()
        self.systeminfo["meta"]["record"] = record_data
        
        t = time.perf_counter()
        self.systeminfo["meta"]["performance"] = {}
        self.systeminfo["meta"]["latency"] = {}
        self.systeminfo["meta"]["performance"]["fps"] = float('nan')
        if self.loop_time is not None:
            self.systeminfo["meta"]["performance"]["fps"] = self.frame_counter / (t - self.loop_time)
            self.frame_counter = 0
        self.loop_time = t
        for k, v in  self.chronos_perf.items():
            self.systeminfo["meta"]["performance"][k] = {
                'num': v.n,
                'min': v.min,
                'avg': v.avg,
                'max': v.max,
            }
            v.reset()
            
        # for k, v in  self.chronos_lat.items():
        #     self.systeminfo["meta"]["latency"][k] = {
        #         'num': v.n,
        #         'min': v.min,
        #         'avg': v.avg,
        #         'max': v.max,
        #     }
        #     v.reset()
            
        self.systeminfo["frame"] = frame_64
        with open(f'{self.conf["session_data_dir"]}diag/{int(datetime.now().timestamp()*1000)}.json', 'w') as f:
            json.dump(self.systeminfo, f, indent=2, cls=JSONEncoder)
        if self.conf['send_deep_diagnostics'] != 'never':
            with open(f'{self.conf["session_data_dir"]}deep_diag/{int(datetime.now().timestamp()*1000)}.json', 'w') as f:
                json.dump(self.systeminfo, f, indent=2, cls=JSONEncoder)
        
    def calc_disk_space(self):
        if not self.enable_writing_diagnostics:
            return
        free = shutil.disk_usage(self.conf['session_data_dir'])[2]
        if free < self.conf['min_free_space']:
            self.enable_writing_diagnostics = False
            return
        total_diagnostics_size = 0
        for dirpath, dirnames, filenames in os.walk(self.conf['data_dir']):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if os.path.isfile(file_path):
                    total_diagnostics_size += os.path.getsize(file_path)
        if total_diagnostics_size > self.conf['max_diagnostic_data_size']:
            self.enable_writing_diagnostics = False

    def calc_disk_space_thread(self):
        while True:
            self.calc_disk_space()
            time.sleep(30)

    def run(self):
        logging.info(f'PC server proc started, PID:{os.getpid()}')
        recv_thread = Thread(target=self.og_api.receive, daemon=True)
        recv_thread.start()
        while True:
            # while self.q_frame_data.qsize() > 0:
            try:
                fd:tuple[FrameData, FrameDump|None, T.Any] = self.q_frame_data.get(timeout=1.)
            except:
                continue
            frame_data, frame_dump, frame = fd
            frame_data.timestamps['pc_pog_start'] = time.perf_counter()
            self.last_timestamp = frame_data.timestamp
            frame_data.timestamp -= self.start_time
            # logging.debug(f'Frame data received, calibr={self.calibr_start}')
            frame_data = self.calibration_model.calc_pog(frame_data)
            frame_data.timestamps['pc_pog_end'] = time.perf_counter()
            record_data = {}
            if self.calibr_start:
                # logging.debug('Put frame data for calibration process')
                self.q_cal_frame_data.put(frame_data)
                while self.q_cal_data.qsize() > 0:
                    cal_data = self.q_cal_data.get()
                    if cal_data is not None:
                        self.og_api.send_calib_result()
                        if not self.emulation:
                            self.calibration_model.calibr_data = cal_data
                            self.calibration_model.save_raw_calibr_data()
                            if self.conf['send_deep_diagnostics'] != 'never':
                                t = datetime.now().strftime('%Y_%m_%d__%H_%M_%S')
                                fn = f"{self.conf['session_data_dir']}deep_diag/raw_calibr_data_{t}.json"
                                self.calibration_model.save_raw_calibr_data(fn=fn)
                    self.last_calibr_ok = cal_data is not None
                    self.calibr_start = False
            else:
                record_data = self.og_api.send_frame_data(frame_data)
                frame_data.timestamps['api_send_end'] = time.perf_counter()
            if 'start' in frame_data.timestamps:
                for k, v in frame_data.times.items():
                    if k not in self.chronos_perf:
                        self.chronos_perf[k] = Chrono()
                    self.chronos_perf[k].append(v)

                for k, v in frame_data.timestamps.items():
                    if k == 'start':
                        continue
                    if k not in self.chronos_perf:
                        self.chronos_lat[k] = Chrono()
                    self.chronos_lat[k].append(v - frame_data.timestamps['start'])
            
            if self.diag_rate  and self.og_api.settings['ENABLE_SEND_DATA'].value:
                if self.loop_time is None:
                    self.loop_time = time.perf_counter()
                    self.frame_counter = 0
                    for k in self.chronos_lat:
                        self.chronos_lat[k].reset()
                    for k in self.chronos_perf:
                        self.chronos_perf[k].reset()
                self.frame_counter += 1                        
                if frame is not None:
                    self.diag_counter += 1
                    if self.diag_counter == self.diag_rate:
                        self.diag_counter = 0
                        self.send_diag_data(frame, record_data)
            else:
                self.loop_time = None
                
            if self.conf['send_deep_diagnostics'] != 'never' and \
                self.enable_writing_diagnostics and \
                frame_dump is not None and \
                (self.og_api.settings['ENABLE_SEND_DATA'].value or \
                 self.og_api.settings['CALIBRATE_START'].value or \
                 self.conf['send_deep_diagnostics'] == 'always'):
                    frame_dump.frame_data = frame_data
                    self.q_diag_data.put(frame_dump)
