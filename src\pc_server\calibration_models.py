#src/pc_server/calibration_models.py
import cv2
import json
from datetime import datetime
import screeninfo
from scipy.spatial.transform import Rotation
from scipy import optimize
from screeninfo import Monitor

from src.common.eye_data import *
from src.common.json_encoder import <PERSON><PERSON><PERSON><PERSON>der
from src.common.config import Conf
from src.common.eye_params import EyeParams
from src.common.gaze_estimation import calculate_gaze


class Param:
    def __init__(self, initial_value, min_value=-np.inf, max_value=np.inf, x_scale=1.0, eye_specific=False):
        self.initial_value = initial_value
        self.min_value = min_value
        self.max_value = max_value
        self.x_scale = x_scale
        self.eye_specific = eye_specific
        

class BaseCalibrationModel:
    """
    Abstract base class for calibration models. Manages calibration data loading and saving.
    Subclasses must implement the 'calibrate' and 'calc_pog' methods.
    """
    def __init__(self, monitor:Monitor, conf:Conf):
        self.monitor = monitor
        self.conf = conf
        self.__calibr_data:list[CalibrPoint] = []
        # Add correction fields
        self.correction_enabled = self.conf.get('calibr_correction_enabled', False)
        self.correction_errors = []  # Store errors for each calibration point
        if self.conf['calibr_load_on_start']: 
            self.load_raw_calibr_data()

    @property
    def calibr_data(self):
        return self.__calibr_data

    @calibr_data.setter
    def calibr_data(self, value):
        """Setter for calibration data. Triggers calibration upon setting new data."""
        self.__calibr_data = value
        if value is not None:
            self.calibrate()

    def calibration_required(self):
        return not len(self.calibr_data)

    def calibrate(self):
        raise NotImplementedError('Not implemented')

    def calc_pog(self, frame_data: FrameData):
        raise NotImplementedError('Not implemented')

    def load_raw_calibr_data(self, fn='raw_calibr_data.json')fix:
        """
        Loads raw calibration data from a JSON file and populates the calibration data attribute.

        Args:
            fn (str, optional): Filename of the calibration data JSON file. Defaults to 'raw_calibr_data.json'.
        """
        j = None
        try:
            with open(fn) as f:
                j = json.load(f)
        except:
            return
        calibr_data = []
        for p in j:
            point = CalibrPoint()
            for eye in point.eye_data:
                point.eye_data[eye].gaze_origin = p[eye]['origin']
                point.eye_data[eye].gaze_vec = p[eye]['gaze_vec']
            point.pog = p['pog']
            calibr_data.append(point)
        self.calibr_data = calibr_data

    def save_raw_calibr_data(self, fn='raw_calibr_data.json')fix:
        """
        Saves the current calibration data to a JSON file using the custom JSON encoder.

        Args:
            fn (str, optional): Filename for saving the calibration data. Defaults to 'raw_calibr_data.json'.
        """
        if self.calibr_data is not None:
            with open(fn, 'w') as f:
                json.dump(self.calibr_data, f, indent=2, cls=JSONEncoder)


class OptimizationCalibrationModel(BaseCalibrationModel):
    def __init__(self, monitor:Monitor, conf:Conf):
        self.R_screen = np.eye(3)
        self.t_screen = np.zeros(3)
        self.eye_params = EyeParams()
        super().__init__(monitor, conf)
        
        x0, y0 = self.p2d_rel_to_p3d_abs_adcs([0.5, 1])[:2]
        typical_distance = 600 #mm
        default_eye_params = EyeParams()
        self.params = {
            'x_screen': Param(x0, x0-200, x0+200, typical_distance),
            'y_screen': Param(y0, -200, y0+200, 1000.0),
            'z_screen': Param(0.0, -200, 200, 1000.0),
            'rx_screen': Param(0.0, -np.pi/2, np.pi/2, 1.0),
            'ry_screen': Param(0.0, -np.pi/4, np.pi/4, 1.0),
            'rz_screen': Param(0.0, -np.pi/4, np.pi/4, 1.0),
            'pupil_distance': Param(default_eye_params.pupil_distance, 0.0, 100.0, 1.0),
            'n': Param(default_eye_params.n, 1.0, 2.0, 1.0),
            'R_left': Param(default_eye_params.R[0], 0.0, 10.0, 1.0, True),
            'R_right': Param(default_eye_params.R[1], 0.0, 10.0, 1.0, True),
            'K_left': Param(default_eye_params.K[0], -10.0, 10.0, 1.0, True),
            'K_right': Param(default_eye_params.K[1], -10.0, 10.0, 1.0, True),
            'alpha_left': Param(default_eye_params.alpha[0], -np.radians(10), 0.0, True),
            'alpha_right': Param(default_eye_params.alpha[1], -np.radians(10), 0.0, 1.0, True),
            'beta_left': Param(default_eye_params.beta[0], -np.radians(5), np.radians(5), 1.0, True),
            'beta_right': Param(default_eye_params.beta[1], -np.radians(5), np.radians(5), 1.0, True),
            'iris_diam_left': Param(default_eye_params.iris_diam[0], 8.0, 15.0, 1.0, True),
            'iris_diam_right': Param(default_eye_params.iris_diam[1], 8.0, 15.0, 1.0, True),
        }
        self.optimized_params = ['x_screen', 'y_screen', 'z_screen', 'rx_screen', 'ry_screen', 'rz_screen']

    def p2d_rel_to_p3d_abs_adcs(self, p2d):
        p3d = [p2d[0]*self.monitor.width_mm, p2d[1]*self.monitor.height_mm, 0.]
        return np.array(p3d) #[p2d[0]*self.monitor.width_mm, p2d[1]*self.monitor.width_mm, 0.])

    def calc_error(self, fds:list[FrameData], p2ds:np.ndarray, R_screen=None, t_screen=None, eye_params=None):
        if R_screen is None:
            R_screen = self.R_screen
        if t_screen is None:
            t_screen = self.t_screen
        if eye_params is None:
            eye_params = self.eye_params
        errors = []
        for fd, p2d in zip(fds, p2ds):
            fd = calculate_gaze(eye_params, self.conf, fd)
            for eye in fd.eye_data:
                origin = eye.gaze_origin
                vec = eye.gaze_vec
                if vec[0] is None or np.isnan(vec[0]):
                    continue
                p2d_gaze = self.do_calc_pog_abs(origin, vec, R_screen, t_screen)
                errors.append(p2d_gaze - self.p2d_rel_to_p3d_abs_adcs(p2d)[:2])
        return errors

    def get_params(self):
        '''
        Returns:
            initial_guess (np.ndarray): Initial guess for the optimization
            bounds (tuple): Bounds for the optimization variables
            
        '''
    def get_params_from_value_vec(self, vals):
        t = vals[0:3]*1000
        rxyz = vals[3:6]
        R = Rotation.from_euler('xyz', rxyz, degrees=False).as_matrix()
        eye_params = EyeParams()
        return R, t, eye_params

    def error_function(self, vals, fds:list[FrameData], p2ds):
        """
        Args:
            vals:
             [0:3] - guessed cam to screen translation vector(meters)
             [3:6] - guessed cam to screen Euler angles (XYZ, radians)
            p2ds: - 2d relative (0-1) screen point coordinates
            gaze_origins: gaze origin point (mm), UCS
            gaze_vecs: gaze vector, UCS

        Returns:
            error vector
        """
        R, t, eye_params = self.get_params_from_value_vec(vals)
        return np.array(self.calc_error(fds, p2ds, R, t, eye_params)).ravel()


    def calibrate(self):
        # print(f"{self.monitor.width_mm}x{self.monitor.height_mm}mm")
        bottom_center = self.p2d_rel_to_p3d_abs_adcs([0.5, 1])
        initial_guess = np.zeros(6)
        initial_guess[0:3] = bottom_center / 1000
        p2ds = []
        origins = []
        vecs = []
        for d in self.calibr_data:
            p2ds.append(d.pog)
            origin = [np.array(d.eye_data[key].gaze_origin) for key in d.eye_data]
            vec = [np.array(d.eye_data[key].gaze_vec) for key in d.eye_data]
            origins.append((origin[0]+origin[1])/2)
            vecs.append((vec[0] + vec[1]) / 2)
        if self.conf['debug']:
            print(f"{initial_guess=}")
        result:optimize.OptimizeResult = optimize.least_squares(self.error_function, initial_guess, args=(p2ds, origins, vecs), loss='soft_l1')
        vals = result.x
        if self.conf['send_deep_diagnostics']:
            with open(f'{self.conf["session_data_dir"]}deep_diag/{int(datetime.now().timestamp())}.txt', 'w') as fp:
                print(result, file=fp)
        self.R_screen, self.t_screen, self.eye_params = self.get_params_from_value_vec(vals)
        # self.t_screen = pose[0:3]*1000
        # rxyz = pose[3:6]
        # self.R_screen = Rotation.from_euler('xyz', rxyz, degrees=False).as_matrix()
        if self.conf['debug']:
            print(self.R_screen, self.t_screen)

    def do_calc_pog_abs(self, origin, gaze_vec, R=None, t=None):
        if R is None:
            R = self.R_screen
        if t is None:
            t = self.t_screen
        origin_screen =  t + R @ origin
        vec_screen = R @ gaze_vec
        if vec_screen[2] == 0:
            return None
        p2d = (origin_screen - (origin_screen[2] / vec_screen[2]) * vec_screen)[:2]
        return p2d

    def calc_pog(self, frame_data: FrameData):
        if self.calibration_required():
            return frame_data
        # Iterate through each eye ('left' and 'right') in the frame data
        for eye in frame_data.eye_data:
            origin = frame_data.eye_data[eye].gaze_origin
            vec = frame_data.eye_data[eye].gaze_vec

            # Skip if the gaze vector is invalid
            if vec[0] is None or np.isnan(vec[0]):
                continue

            # calculate gaze point in mm
            p2d = self.do_calc_pog_abs(origin, vec)
            if p2d is None:
                continue

            # normalize data to screen dimensions
            p2d_rel = np.array([p2d[0]/self.monitor.width_mm, 
                               p2d[1]/self.monitor.height_mm])
            
            # Apply correction if enabled
            if self.correction_enabled and self.correction_errors:
                # Calculate weighted correction based on distance to calibration points
                weights = []
                corrections = []
                
                for cal_point, error_vec in self.correction_errors:
                    # Calculate distance to calibration point
                    dist = np.linalg.norm(p2d_rel - cal_point)
                    # Use Gaussian weighting function instead of inverse distance
                    # This creates smoother transitions between calibration points
                    sigma = 0.2  # Controls influence radius
                    weight = np.exp(-(dist**2)/(2*sigma**2))
                    weights.append(weight)
                    corrections.append(error_vec)
                
                # Normalize weights
                weights = np.array(weights)
                weights /= np.sum(weights)
                
                # Apply weighted correction
                total_correction = np.sum([w * c for w, c in zip(weights, corrections)], axis=0)
                p2d_rel += total_correction
                
            # Clamp to screen bounds
            if self.conf['clip_pog_data']:
                p2d_rel = np.clip(p2d_rel, 0.0, 1.0)
            
            frame_data.eye_data[eye].pog = p2d_rel
        
        return frame_data


# Assign the TiltCalibrationModel to CalibrationModel for use in other modules
# CalibrationModel = TiltCalibrationModel
CalibrationModel = OptimizationCalibrationModel

def test():
    monitor = screeninfo.get_monitors()[0]
    model = CalibrationModel(monitor)
    model.load_raw_calibr_data()
    # model.calibrate()

if __name__ == '__main__':
    test()
