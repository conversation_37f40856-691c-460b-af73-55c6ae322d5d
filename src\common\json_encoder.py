import json
import numpy as np
from src.common.eye_data import *


class JSONEncoder(json.JSONEncoder):
    """ Special json encoder for numpy types """
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, CalibrPoint): # TODO: fix
            res = {}
            for eye, data in obj.eye_data.items():
                res[eye] = {'origin': data.gaze_origin, 'gaze_vec': data.gaze_vec}
            res['pog'] = obj.pog
            return res
        if isinstance(obj, FrameData): # TODO: fix
            res = {}
            for eye, data in obj.eye_data.items():
                res[eye] = {'origin': data.gaze_origin, 'gaze_vec': data.gaze_vec}
            res['timestamp'] = obj.timestamp
            res['ticks'] = obj.ticks
            return res
        return json.JSONEncoder.default(self, obj)